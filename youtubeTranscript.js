const https = require('https');
const rateLimit = require('express-rate-limit');
const { getSubtitles } = require('youtube-captions-scraper');
const ytdl = require('ytdl-core');

module.exports = (app) => {
  // Tiered rate limiting
  const basicLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 50, // transcript operations
    message: 'Too many basic requests from this IP, please try again later.',
  });

  const aiLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 10, // AI operations
    message: 'Too many AI requests from this IP, please try again later.',
  });

  const heavyLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 5, // heavy operations like anki-cards
    message: 'Too many heavy requests from this IP, please try again later.',
  });

  // Validate YouTube video ID
  const validateVideoId = (videoId) => {
    return /^[a-zA-Z0-9_-]{11}$/.test(videoId);
  };

  // Get transcript from YouTube
  const getTranscript = async (videoId, language = 'en') => {
    try {
      // Try youtube-captions-scraper first
      const captions = await getSubtitles({
        videoID: videoId,
        lang: language
      });

      const transcript = captions.map((caption, index) => ({
        start: caption.start,
        end: caption.start + caption.dur,
        text: caption.text,
        formattedStart: formatTime(caption.start),
        formattedEnd: formatTime(caption.start + caption.dur)
      }));

      return {
        transcript,
        language,
        videoId,
        source: 'captions-scraper'
      };
    } catch (error) {
      throw new Error(`Failed to fetch transcript: ${error.message}`);
    }
  };

  // Format time helper
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Generate AI content using Gemini
  const generateAIContent = async (prompt, options = {}) => {
    if (!process.env.GOOGLE_API_KEY) {
      throw new Error('Google API key not configured');
    }

    const model = options.model || 'gemini-2.5-flash';
    const requestData = {
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxOutputTokens || 2048,
        thinkingConfig: { thinkingBudget: 0 }
      }
    };

    const geminiRequestData = JSON.stringify(requestData);
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${process.env.GOOGLE_API_KEY}`;
    const url = new URL(geminiUrl);

    return new Promise((resolve, reject) => {
      const options = {
        hostname: url.hostname,
        port: 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(geminiRequestData),
        },
      };

      const geminiRequest = https.request(options, (geminiResponse) => {
        let responseData = '';

        geminiResponse.on('data', (chunk) => {
          responseData += chunk;
        });

        geminiResponse.on('end', () => {
          try {
            const jsonResponse = JSON.parse(responseData);
            if (jsonResponse.candidates && jsonResponse.candidates[0]) {
              resolve(jsonResponse.candidates[0].content.parts[0].text);
            } else {
              reject(new Error('Invalid response from Gemini API'));
            }
          } catch (error) {
            reject(new Error('Failed to parse Gemini API response'));
          }
        });
      });

      geminiRequest.on('error', (error) => {
        reject(new Error(`Failed to connect to Gemini API: ${error.message}`));
      });

      geminiRequest.write(geminiRequestData);
      geminiRequest.end();
    });
  };

  // Main endpoint
  app.post('/youtube-transcript', async (req, res) => {
    try {
      const { operation, videoId, language = 'en', options = {} } = req.body;

      // Validate input
      if (!operation || !videoId) {
        return res.status(400).json({
          error: 'Operation and videoId are required'
        });
      }

      if (!validateVideoId(videoId)) {
        return res.status(400).json({
          error: 'Invalid YouTube video ID format'
        });
      }

      const validOperations = ['transcript', 'summary', 'quotes', 'description', 'anki-cards', 'social-media'];
      if (!validOperations.includes(operation)) {
        return res.status(400).json({
          error: 'Invalid operation type'
        });
      }

      // Apply appropriate rate limiting
      let limiter;
      if (operation === 'transcript') {
        limiter = basicLimiter;
      } else if (operation === 'anki-cards') {
        limiter = heavyLimiter;
      } else {
        limiter = aiLimiter;
      }

      // Apply rate limiting
      await new Promise((resolve, reject) => {
        limiter(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // Get transcript data
      const transcriptData = await getTranscript(videoId, language);
      const transcriptText = transcriptData.transcript.map(t => t.text).join(' ');

      // Handle different operations
      switch (operation) {
        case 'transcript':
          return res.json({
            success: true,
            ...transcriptData
          });

        case 'summary':
          const summaryPrompt = `Please provide a comprehensive summary of this video transcript:\n\n${transcriptText}`;
          const summary = await generateAIContent(summaryPrompt, options);
          return res.json({
            success: true,
            videoId,
            language,
            summary,
            model: options.model || 'gemini-2.5-flash'
          });

        case 'quotes':
          const quotesPrompt = `Extract 5-10 key quotes from this video transcript. Return as JSON array with format: [{"quote": "text", "timestamp": seconds, "context": "brief context"}]\n\n${transcriptText}`;
          const quotesResponse = await generateAIContent(quotesPrompt, options);
          let quotes = [];
          try {
            const jsonMatch = quotesResponse.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
              quotes = JSON.parse(jsonMatch[0]);
            }
          } catch (e) {
            console.error('Error parsing quotes JSON:', e);
          }
          return res.json({
            success: true,
            videoId,
            language,
            quotes,
            model: options.model || 'gemini-2.5-flash'
          });

        case 'anki-cards':
          const wordCount = transcriptText.split(' ').length;
          const targetCardCount = Math.max(5, Math.min(25, Math.floor(wordCount / 200)));
          
          const ankiPrompt = `Create ${targetCardCount} educational flashcards from this video transcript. Return as JSON array with format: [{"type": "concept|application|insight", "question": "string", "answer": "string", "timestamp": number, "difficulty": "beginner|intermediate|advanced", "tags": ["tag1", "tag2"], "context": "general principle"}]\n\n${transcriptText}`;
          const ankiResponse = await generateAIContent(ankiPrompt, { ...options, maxOutputTokens: 4096 });
          let cards = [];
          try {
            const jsonMatch = ankiResponse.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
              cards = JSON.parse(jsonMatch[0]);
            }
          } catch (e) {
            console.error('Error parsing anki cards JSON:', e);
          }
          return res.json({
            success: true,
            videoId,
            language,
            cards,
            metadata: {
              transcriptLength: wordCount,
              targetCardCount,
              actualCardCount: cards.length,
              model: options.model || 'gemini-2.5-flash'
            }
          });

        default:
          return res.status(400).json({
            error: 'Operation not yet implemented'
          });
      }

    } catch (error) {
      console.error('Error in youtube-transcript route:', error);
      res.status(500).json({
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });
};