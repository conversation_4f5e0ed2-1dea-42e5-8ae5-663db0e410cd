const https = require('https');
const rateLimit = require('express-rate-limit');
const { getSubtitles } = require('youtube-captions-scraper');
const ytdl = require('ytdl-core');

module.exports = (app) => {
  // Tiered rate limiting
  const basicLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 50, // transcript operations
    message: 'Too many basic requests from this IP, please try again later.',
  });

  const aiLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 10, // AI operations
    message: 'Too many AI requests from this IP, please try again later.',
  });

  const heavyLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 5, // heavy operations like anki-cards
    message: 'Too many heavy requests from this IP, please try again later.',
  });

  // Validate YouTube video ID
  const validateVideoId = (videoId) => {
    return /^[a-zA-Z0-9_-]{11}$/.test(videoId);
  };

  // Human-like request headers to avoid detection
  const getRandomUserAgent = () => {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  };

  // Add random delays to mimic human behavior
  const humanDelay = () => {
    const delay = Math.random() * 2000 + 1000; // 1-3 seconds
    return new Promise(resolve => setTimeout(resolve, delay));
  };

  // Fallback method using ytdl-core with human-like behavior
  const getTranscriptWithYtdl = async (videoId, language = 'en') => {
    try {
      await humanDelay(); // Random delay before request

      const info = await ytdl.getInfo(videoId, {
        requestOptions: {
          headers: {
            'User-Agent': getRandomUserAgent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
          }
        }
      });

      // Look for caption tracks
      const captionTracks = info.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];

      let targetTrack = captionTracks.find(track => track.languageCode === language);
      if (!targetTrack && captionTracks.length > 0) {
        targetTrack = captionTracks[0]; // Fallback to first available
      }

      if (!targetTrack) {
        throw new Error('No captions available');
      }

      // Fetch the caption content
      const captionUrl = targetTrack.baseUrl;
      const captionResponse = await fetch(captionUrl, {
        headers: {
          'User-Agent': getRandomUserAgent(),
        }
      });

      const captionXml = await captionResponse.text();
      const transcript = parseCaptionXML(captionXml);

      return {
        transcript,
        language: targetTrack.languageCode,
        videoId,
        source: 'ytdl-core'
      };
    } catch (error) {
      throw new Error(`ytdl-core fallback failed: ${error.message}`);
    }
  };

  // Parse XML captions to our format
  const parseCaptionXML = (xmlString) => {
    const transcript = [];
    // Simple regex parsing (you might want to use a proper XML parser)
    const textMatches = xmlString.match(/<text start="([^"]*)" dur="([^"]*)"[^>]*>([^<]*)<\/text>/g);

    if (textMatches) {
      textMatches.forEach(match => {
        const startMatch = match.match(/start="([^"]*)"/);
        const durMatch = match.match(/dur="([^"]*)"/);
        const textMatch = match.match(/>([^<]*)</);

        if (startMatch && durMatch && textMatch) {
          const start = parseFloat(startMatch[1]);
          const dur = parseFloat(durMatch[1]);
          const text = textMatch[1].replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '"');

          transcript.push({
            start,
            end: start + dur,
            text,
            formattedStart: formatTime(start),
            formattedEnd: formatTime(start + dur)
          });
        }
      });
    }

    return transcript;
  };

  // Main transcript function with intelligent fallbacks
  const getTranscript = async (videoId, language = 'en') => {
    const methods = [
      {
        name: 'captions-scraper',
        fn: async () => {
          const captions = await getSubtitles({
            videoID: videoId,
            lang: language
          });

          const transcript = captions.map((caption) => ({
            start: caption.start,
            end: caption.start + caption.dur,
            text: caption.text,
            formattedStart: formatTime(caption.start),
            formattedEnd: formatTime(caption.start + caption.dur)
          }));

          return {
            transcript,
            language,
            videoId,
            source: 'captions-scraper'
          };
        }
      },
      {
        name: 'ytdl-core',
        fn: () => getTranscriptWithYtdl(videoId, language)
      }
    ];

    let lastError;

    for (const method of methods) {
      try {
        console.log(`Trying ${method.name} for video ${videoId}`);
        const result = await method.fn();

        if (result && result.transcript && result.transcript.length > 0) {
          console.log(`Success with ${method.name} for video ${videoId}`);
          return result;
        }
      } catch (error) {
        console.log(`${method.name} failed for video ${videoId}: ${error.message}`);
        lastError = error;

        // Add delay between methods to avoid rapid-fire requests
        if (method.name !== methods[methods.length - 1].name) {
          await humanDelay();
        }
      }
    }

    throw new Error(`All transcript methods failed. Last error: ${lastError?.message}`);
  };

  // Format time helper
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Generate AI content using Gemini
  const generateAIContent = async (prompt, options = {}) => {
    if (!process.env.GOOGLE_API_KEY) {
      throw new Error('Google API key not configured');
    }

    const model = options.model || 'gemini-2.5-flash';
    const requestData = {
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxOutputTokens || 2048,
        thinkingConfig: { thinkingBudget: 0 }
      }
    };

    const geminiRequestData = JSON.stringify(requestData);
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${process.env.GOOGLE_API_KEY}`;
    const url = new URL(geminiUrl);

    return new Promise((resolve, reject) => {
      const options = {
        hostname: url.hostname,
        port: 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(geminiRequestData),
        },
      };

      const geminiRequest = https.request(options, (geminiResponse) => {
        let responseData = '';

        geminiResponse.on('data', (chunk) => {
          responseData += chunk;
        });

        geminiResponse.on('end', () => {
          try {
            const jsonResponse = JSON.parse(responseData);
            if (jsonResponse.candidates && jsonResponse.candidates[0]) {
              resolve(jsonResponse.candidates[0].content.parts[0].text);
            } else {
              reject(new Error('Invalid response from Gemini API'));
            }
          } catch (error) {
            reject(new Error('Failed to parse Gemini API response'));
          }
        });
      });

      geminiRequest.on('error', (error) => {
        reject(new Error(`Failed to connect to Gemini API: ${error.message}`));
      });

      geminiRequest.write(geminiRequestData);
      geminiRequest.end();
    });
  };

  // Main endpoint
  app.post('/youtube-transcript', async (req, res) => {
    try {
      const { operation, videoId, language = 'en', options = {} } = req.body;

      // Validate input
      if (!operation || !videoId) {
        return res.status(400).json({
          error: 'Operation and videoId are required'
        });
      }

      if (!validateVideoId(videoId)) {
        return res.status(400).json({
          error: 'Invalid YouTube video ID format'
        });
      }

      const validOperations = ['transcript', 'summary', 'quotes', 'description', 'anki-cards', 'social-media'];
      if (!validOperations.includes(operation)) {
        return res.status(400).json({
          error: 'Invalid operation type'
        });
      }

      // Apply appropriate rate limiting
      let limiter;
      if (operation === 'transcript') {
        limiter = basicLimiter;
      } else if (operation === 'anki-cards') {
        limiter = heavyLimiter;
      } else {
        limiter = aiLimiter;
      }

      // Apply rate limiting
      await new Promise((resolve, reject) => {
        limiter(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // Get transcript data
      const transcriptData = await getTranscript(videoId, language);
      const transcriptText = transcriptData.transcript.map(t => t.text).join(' ');

      // Handle different operations
      switch (operation) {
        case 'transcript':
          return res.json({
            success: true,
            ...transcriptData
          });

        case 'summary':
          const summaryPrompt = `Please provide a comprehensive summary of this video transcript:\n\n${transcriptText}`;
          const summary = await generateAIContent(summaryPrompt, options);
          return res.json({
            success: true,
            videoId,
            language,
            summary,
            model: options.model || 'gemini-2.5-flash'
          });

        case 'quotes':
          const quotesPrompt = `Extract 5-10 key quotes from this video transcript. Return as JSON array with format: [{"quote": "text", "timestamp": seconds, "context": "brief context"}]\n\n${transcriptText}`;
          const quotesResponse = await generateAIContent(quotesPrompt, options);
          let quotes = [];
          try {
            const jsonMatch = quotesResponse.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
              quotes = JSON.parse(jsonMatch[0]);
            }
          } catch (e) {
            console.error('Error parsing quotes JSON:', e);
          }
          return res.json({
            success: true,
            videoId,
            language,
            quotes,
            model: options.model || 'gemini-2.5-flash'
          });

        case 'anki-cards':
          const wordCount = transcriptText.split(' ').length;
          const targetCardCount = Math.max(5, Math.min(25, Math.floor(wordCount / 200)));
          
          const ankiPrompt = `Create ${targetCardCount} educational flashcards from this video transcript. Return as JSON array with format: [{"type": "concept|application|insight", "question": "string", "answer": "string", "timestamp": number, "difficulty": "beginner|intermediate|advanced", "tags": ["tag1", "tag2"], "context": "general principle"}]\n\n${transcriptText}`;
          const ankiResponse = await generateAIContent(ankiPrompt, { ...options, maxOutputTokens: 4096 });
          let cards = [];
          try {
            const jsonMatch = ankiResponse.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
              cards = JSON.parse(jsonMatch[0]);
            }
          } catch (e) {
            console.error('Error parsing anki cards JSON:', e);
          }
          return res.json({
            success: true,
            videoId,
            language,
            cards,
            metadata: {
              transcriptLength: wordCount,
              targetCardCount,
              actualCardCount: cards.length,
              model: options.model || 'gemini-2.5-flash'
            }
          });

        default:
          return res.status(400).json({
            error: 'Operation not yet implemented'
          });
      }

    } catch (error) {
      console.error('Error in youtube-transcript route:', error);
      res.status(500).json({
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });
};