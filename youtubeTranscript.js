const https = require('https');
const rateLimit = require('express-rate-limit');
const { getSubtitles } = require('youtube-captions-scraper');
const transcriptLogger = require('./utils/transcriptLogger');

module.exports = (app) => {
  // Tiered rate limiting
  const basicLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 50, // transcript operations
    message: 'Too many basic requests from this IP, please try again later.',
  });

  const aiLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 10, // AI operations
    message: 'Too many AI requests from this IP, please try again later.',
  });

  const heavyLimiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 5, // heavy operations like anki-cards
    message: 'Too many heavy requests from this IP, please try again later.',
  });

  // Validate YouTube video ID
  const validateVideoId = (videoId) => {
    return /^[a-zA-Z0-9_-]{11}$/.test(videoId);
  };

  // Human-like request headers to avoid detection
  const getRandomUserAgent = () => {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
  };

  // Add random delays to mimic human behavior
  const humanDelay = () => {
    const delay = Math.random() * 2000 + 1000; // 1-3 seconds
    return new Promise(resolve => setTimeout(resolve, delay));
  };

  // Enhanced captions-scraper method with retry logic
  const getTranscriptWithCaptionsScraper = async (videoId, language = 'en') => {
    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[${new Date().toISOString()}] Attempting captions-scraper for video ${videoId} (attempt ${attempt}/${maxRetries})`);

        // Add random delay to appear more human-like
        if (attempt > 1) {
          await humanDelay();
        }

        const captions = await getSubtitles({
          videoID: videoId,
          lang: language
        });

        if (!captions || captions.length === 0) {
          throw new Error('No captions returned from scraper');
        }

        const transcript = captions.map((caption) => ({
          start: caption.start,
          end: caption.start + caption.dur,
          text: caption.text,
          formattedStart: formatTime(caption.start),
          formattedEnd: formatTime(caption.start + caption.dur)
        }));

        console.log(`[${new Date().toISOString()}] Successfully scraped ${transcript.length} caption segments`);

        return {
          transcript,
          language,
          videoId,
          source: 'captions-scraper'
        };
      } catch (error) {
        lastError = error;
        console.log(`[${new Date().toISOString()}] Captions-scraper attempt ${attempt} failed: ${error.message}`);

        if (attempt < maxRetries) {
          // Wait longer between retries
          await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
        }
      }
    }

    throw new Error(`Captions-scraper failed after ${maxRetries} attempts: ${lastError?.message}`);
  };

  // Parse XML captions to our format with better error handling
  const parseCaptionXML = (xmlString) => {
    const transcript = [];

    try {
      // Handle different XML formats that YouTube might return
      let textMatches = xmlString.match(/<text start="([^"]*)" dur="([^"]*)"[^>]*>([^<]*)<\/text>/g);

      // Fallback pattern for different XML structures
      if (!textMatches) {
        textMatches = xmlString.match(/<text[^>]*start="([^"]*)"[^>]*dur="([^"]*)"[^>]*>([^<]*)<\/text>/g);
      }

      // Another fallback for simpler structures
      if (!textMatches) {
        textMatches = xmlString.match(/<text[^>]*>([^<]*)<\/text>/g);
      }

      if (textMatches) {
        textMatches.forEach((match, index) => {
          try {
            const startMatch = match.match(/start="([^"]*)"/);
            const durMatch = match.match(/dur="([^"]*)"/);
            const textMatch = match.match(/>([^<]*)</);

            if (startMatch && durMatch && textMatch) {
              const start = parseFloat(startMatch[1]);
              const dur = parseFloat(durMatch[1]);

              // Better text cleaning
              let text = textMatch[1]
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/&apos;/g, "'")
                .trim();

              // Skip empty text segments
              if (text && text.length > 0) {
                transcript.push({
                  start,
                  end: start + dur,
                  text,
                  formattedStart: formatTime(start),
                  formattedEnd: formatTime(start + dur)
                });
              }
            } else if (textMatch) {
              // Fallback for entries without timing info
              let text = textMatch[1]
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/&apos;/g, "'")
                .trim();

              if (text && text.length > 0) {
                transcript.push({
                  start: index * 5, // Estimate timing
                  end: (index + 1) * 5,
                  text,
                  formattedStart: formatTime(index * 5),
                  formattedEnd: formatTime((index + 1) * 5)
                });
              }
            }
          } catch (parseError) {
            console.warn(`Error parsing caption segment: ${parseError.message}`);
            // Continue with next segment
          }
        });
      }
    } catch (error) {
      console.error(`Error parsing caption XML: ${error.message}`);
      throw new Error('Failed to parse caption data');
    }

    return transcript;
  };

  // Main transcript function with enhanced logging
  const getTranscript = async (videoId, language = 'en', requestInfo = {}) => {
    const startTime = Date.now();
    let lastError;

    try {
      console.log(`[${new Date().toISOString()}] Starting transcript request for video ${videoId}`);

      const result = await getTranscriptWithCaptionsScraper(videoId, language);

      if (result && result.transcript && result.transcript.length > 0) {
        const totalDuration = Date.now() - startTime;

        console.log(`[${new Date().toISOString()}] Successfully retrieved transcript for video ${videoId} (${totalDuration}ms)`);

        // Log successful request
        transcriptLogger.logRequest({
          videoId,
          language,
          operation: requestInfo.operation || 'transcript',
          clientIP: requestInfo.clientIP,
          userAgent: requestInfo.userAgent,
          success: true,
          method: 'captions-scraper',
          duration: totalDuration,
          transcriptLength: result.transcript.length,
          error: null
        });

        return result;
      } else {
        throw new Error('No transcript data returned');
      }
    } catch (error) {
      lastError = error;
      console.error(`[${new Date().toISOString()}] Transcript request failed for video ${videoId}: ${error.message}`);
    }

    // Log failed request
    const totalDuration = Date.now() - startTime;
    transcriptLogger.logRequest({
      videoId,
      language,
      operation: requestInfo.operation || 'transcript',
      clientIP: requestInfo.clientIP,
      userAgent: requestInfo.userAgent,
      success: false,
      method: 'captions-scraper',
      duration: totalDuration,
      transcriptLength: 0,
      error: lastError?.message || 'Unknown error'
    });

    throw new Error(`Transcript request failed: ${lastError?.message}`);
  };

  // Format time helper
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Generate AI content using Gemini
  const generateAIContent = async (prompt, options = {}) => {
    if (!process.env.GOOGLE_API_KEY) {
      throw new Error('Google API key not configured');
    }

    const model = options.model || 'gemini-2.5-flash';
    const requestData = {
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        temperature: options.temperature || 0.7,
        maxOutputTokens: options.maxOutputTokens || 2048,
        thinkingConfig: { thinkingBudget: 0 }
      }
    };

    const geminiRequestData = JSON.stringify(requestData);
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${process.env.GOOGLE_API_KEY}`;
    const url = new URL(geminiUrl);

    return new Promise((resolve, reject) => {
      const options = {
        hostname: url.hostname,
        port: 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(geminiRequestData),
        },
      };

      const geminiRequest = https.request(options, (geminiResponse) => {
        let responseData = '';

        geminiResponse.on('data', (chunk) => {
          responseData += chunk;
        });

        geminiResponse.on('end', () => {
          try {
            const jsonResponse = JSON.parse(responseData);
            if (jsonResponse.candidates && jsonResponse.candidates[0]) {
              resolve(jsonResponse.candidates[0].content.parts[0].text);
            } else {
              reject(new Error('Invalid response from Gemini API'));
            }
          } catch (error) {
            reject(new Error('Failed to parse Gemini API response'));
          }
        });
      });

      geminiRequest.on('error', (error) => {
        reject(new Error(`Failed to connect to Gemini API: ${error.message}`));
      });

      geminiRequest.write(geminiRequestData);
      geminiRequest.end();
    });
  };

  // Main endpoint
  app.post('/youtube-transcript', async (req, res) => {
    try {
      const { operation, videoId, language = 'en', options = {} } = req.body;

      // Validate input
      if (!operation || !videoId) {
        return res.status(400).json({
          error: 'Operation and videoId are required'
        });
      }

      if (!validateVideoId(videoId)) {
        return res.status(400).json({
          error: 'Invalid YouTube video ID format'
        });
      }

      const validOperations = ['transcript', 'summary', 'quotes', 'description', 'anki-cards', 'social-media'];
      if (!validOperations.includes(operation)) {
        return res.status(400).json({
          error: 'Invalid operation type'
        });
      }

      // Apply appropriate rate limiting
      let limiter;
      if (operation === 'transcript') {
        limiter = basicLimiter;
      } else if (operation === 'anki-cards') {
        limiter = heavyLimiter;
      } else {
        limiter = aiLimiter;
      }

      // Apply rate limiting
      await new Promise((resolve, reject) => {
        limiter(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // Prepare request info for logging
      const requestInfo = {
        operation,
        clientIP: req.ip || req.connection.remoteAddress || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown'
      };

      // Get transcript data
      const transcriptData = await getTranscript(videoId, language, requestInfo);
      const transcriptText = transcriptData.transcript.map(t => t.text).join(' ');

      // Handle different operations
      switch (operation) {
        case 'transcript':
          return res.json({
            success: true,
            ...transcriptData
          });

        case 'summary':
          const summaryPrompt = `Please provide a comprehensive summary of this video transcript:\n\n${transcriptText}`;
          const summary = await generateAIContent(summaryPrompt, options);
          return res.json({
            success: true,
            videoId,
            language,
            summary,
            model: options.model || 'gemini-2.5-flash'
          });

        case 'quotes':
          const quotesPrompt = `Extract 5-10 key quotes from this video transcript. Return as JSON array with format: [{"quote": "text", "timestamp": seconds, "context": "brief context"}]\n\n${transcriptText}`;
          const quotesResponse = await generateAIContent(quotesPrompt, options);
          let quotes = [];
          try {
            const jsonMatch = quotesResponse.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
              quotes = JSON.parse(jsonMatch[0]);
            }
          } catch (e) {
            console.error('Error parsing quotes JSON:', e);
          }
          return res.json({
            success: true,
            videoId,
            language,
            quotes,
            model: options.model || 'gemini-2.5-flash'
          });

        case 'anki-cards':
          const wordCount = transcriptText.split(' ').length;
          const targetCardCount = Math.max(5, Math.min(25, Math.floor(wordCount / 200)));
          
          const ankiPrompt = `Create ${targetCardCount} educational flashcards from this video transcript. Return as JSON array with format: [{"type": "concept|application|insight", "question": "string", "answer": "string", "timestamp": number, "difficulty": "beginner|intermediate|advanced", "tags": ["tag1", "tag2"], "context": "general principle"}]\n\n${transcriptText}`;
          const ankiResponse = await generateAIContent(ankiPrompt, { ...options, maxOutputTokens: 4096 });
          let cards = [];
          try {
            const jsonMatch = ankiResponse.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
              cards = JSON.parse(jsonMatch[0]);
            }
          } catch (e) {
            console.error('Error parsing anki cards JSON:', e);
          }
          return res.json({
            success: true,
            videoId,
            language,
            cards,
            metadata: {
              transcriptLength: wordCount,
              targetCardCount,
              actualCardCount: cards.length,
              model: options.model || 'gemini-2.5-flash'
            }
          });

        default:
          return res.status(400).json({
            error: 'Operation not yet implemented'
          });
      }

    } catch (error) {
      console.error('Error in youtube-transcript route:', error);
      res.status(500).json({
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });

  // Logging and statistics endpoint
  app.get('/youtube-transcript/stats', (req, res) => {
    try {
      const report = transcriptLogger.generateReport();
      res.json({
        success: true,
        ...report
      });
    } catch (error) {
      console.error('Error generating transcript stats:', error);
      res.status(500).json({
        error: 'Failed to generate statistics',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });

  // Cleanup endpoint (for maintenance)
  app.post('/youtube-transcript/cleanup', (req, res) => {
    try {
      transcriptLogger.cleanupLogs();
      res.json({
        success: true,
        message: 'Log cleanup completed'
      });
    } catch (error) {
      console.error('Error during log cleanup:', error);
      res.status(500).json({
        error: 'Failed to cleanup logs',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });
};