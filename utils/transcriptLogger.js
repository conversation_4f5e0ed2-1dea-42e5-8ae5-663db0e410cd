const fs = require('fs');
const path = require('path');

class TranscriptLogger {
  constructor() {
    this.logDir = path.join(__dirname, '../logs');
    this.statsFile = path.join(this.logDir, 'transcript-stats.json');
    this.requestsFile = path.join(this.logDir, 'transcript-requests.log');
    
    // Ensure logs directory exists
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
    
    // Initialize stats if file doesn't exist
    this.initializeStats();
  }

  initializeStats() {
    if (!fs.existsSync(this.statsFile)) {
      const initialStats = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        methodStats: {
          'captions-scraper': { attempts: 0, successes: 0, failures: 0 },
          'ytdl-core': { attempts: 0, successes: 0, failures: 0 }
        },
        languageStats: {},
        dailyStats: {},
        lastReset: new Date().toISOString()
      };
      fs.writeFileSync(this.statsFile, JSON.stringify(initialStats, null, 2));
    }
  }

  getStats() {
    try {
      const statsData = fs.readFileSync(this.statsFile, 'utf8');
      return JSON.parse(statsData);
    } catch (error) {
      console.error('Error reading stats file:', error);
      this.initializeStats();
      return this.getStats();
    }
  }

  updateStats(statsUpdate) {
    try {
      const stats = this.getStats();
      
      // Update counters
      stats.totalRequests++;
      if (statsUpdate.success) {
        stats.successfulRequests++;
      } else {
        stats.failedRequests++;
      }

      // Update method stats
      if (statsUpdate.method) {
        if (!stats.methodStats[statsUpdate.method]) {
          stats.methodStats[statsUpdate.method] = { attempts: 0, successes: 0, failures: 0 };
        }
        stats.methodStats[statsUpdate.method].attempts++;
        if (statsUpdate.success) {
          stats.methodStats[statsUpdate.method].successes++;
        } else {
          stats.methodStats[statsUpdate.method].failures++;
        }
      }

      // Update language stats
      if (statsUpdate.language) {
        if (!stats.languageStats[statsUpdate.language]) {
          stats.languageStats[statsUpdate.language] = { requests: 0, successes: 0 };
        }
        stats.languageStats[statsUpdate.language].requests++;
        if (statsUpdate.success) {
          stats.languageStats[statsUpdate.language].successes++;
        }
      }

      // Update daily stats
      const today = new Date().toISOString().split('T')[0];
      if (!stats.dailyStats[today]) {
        stats.dailyStats[today] = { requests: 0, successes: 0, failures: 0 };
      }
      stats.dailyStats[today].requests++;
      if (statsUpdate.success) {
        stats.dailyStats[today].successes++;
      } else {
        stats.dailyStats[today].failures++;
      }

      // Keep only last 30 days of daily stats
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];
      
      Object.keys(stats.dailyStats).forEach(date => {
        if (date < cutoffDate) {
          delete stats.dailyStats[date];
        }
      });

      fs.writeFileSync(this.statsFile, JSON.stringify(stats, null, 2));
    } catch (error) {
      console.error('Error updating stats:', error);
    }
  }

  logRequest(requestData) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      videoId: requestData.videoId,
      language: requestData.language,
      operation: requestData.operation,
      clientIP: requestData.clientIP,
      userAgent: requestData.userAgent,
      success: requestData.success,
      method: requestData.method,
      duration: requestData.duration,
      error: requestData.error,
      transcriptLength: requestData.transcriptLength
    };

    const logLine = JSON.stringify(logEntry) + '\n';
    
    try {
      fs.appendFileSync(this.requestsFile, logLine);
    } catch (error) {
      console.error('Error writing to request log:', error);
    }

    // Update stats
    this.updateStats({
      success: requestData.success,
      method: requestData.method,
      language: requestData.language
    });
  }

  getSuccessRate() {
    const stats = this.getStats();
    if (stats.totalRequests === 0) return 0;
    return (stats.successfulRequests / stats.totalRequests * 100).toFixed(2);
  }

  getMethodSuccessRates() {
    const stats = this.getStats();
    const rates = {};
    
    Object.keys(stats.methodStats).forEach(method => {
      const methodStats = stats.methodStats[method];
      if (methodStats.attempts > 0) {
        rates[method] = {
          successRate: (methodStats.successes / methodStats.attempts * 100).toFixed(2),
          attempts: methodStats.attempts,
          successes: methodStats.successes,
          failures: methodStats.failures
        };
      }
    });
    
    return rates;
  }

  getDailyStats(days = 7) {
    const stats = this.getStats();
    const result = {};
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      result[dateStr] = stats.dailyStats[dateStr] || { requests: 0, successes: 0, failures: 0 };
    }
    
    return result;
  }

  generateReport() {
    const stats = this.getStats();
    const methodRates = this.getMethodSuccessRates();
    const dailyStats = this.getDailyStats(7);
    
    return {
      summary: {
        totalRequests: stats.totalRequests,
        successfulRequests: stats.successfulRequests,
        failedRequests: stats.failedRequests,
        overallSuccessRate: this.getSuccessRate() + '%'
      },
      methodPerformance: methodRates,
      languageBreakdown: stats.languageStats,
      last7Days: dailyStats,
      lastUpdated: new Date().toISOString()
    };
  }

  // Clean up old log files (keep last 30 days)
  cleanupLogs() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      // For now, we'll just rotate the request log if it gets too large (>10MB)
      const stats = fs.statSync(this.requestsFile);
      const fileSizeInMB = stats.size / (1024 * 1024);
      
      if (fileSizeInMB > 10) {
        const backupFile = this.requestsFile.replace('.log', `-backup-${Date.now()}.log`);
        fs.renameSync(this.requestsFile, backupFile);
        console.log(`Rotated large log file to: ${backupFile}`);
      }
    } catch (error) {
      console.error('Error during log cleanup:', error);
    }
  }
}

module.exports = new TranscriptLogger();
