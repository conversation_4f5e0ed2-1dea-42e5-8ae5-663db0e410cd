const http = require('http');
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
require('dotenv').config({ path: `${__dirname}/.env` });
const fs = require('fs');

// Server port (now defined via environment variables so that it automatically picks up either the port for the prod or the dev server)
const port = process.env.PORT;

const app = express();
const server = http.createServer(app);

app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json({
  verify(req, res, buf) {
    req.rawBody = buf;
  },
  limit: '2mb',
}));
// app.use(cors({
//   origin: [
//     'https://www.whispertranscribe.com',
//     'https://whispertranscribe.com',
//   ],
// }));
app.use(cors());


// CREATE LOGS FOLDER IF NECESSARY
const logFolderPath = `${__dirname}/logs`;
if (!fs.existsSync(logFolderPath)) fs.mkdirSync(logFolderPath);

const PodcastNameGenerator = require('./routes/podcastNameGenerator');
PodcastNameGenerator(app);

const YouTubeTranscript = require('./youtubeTranscript');
YouTubeTranscript(app);

// Start listening to requests
const serverInstance = server.listen(port, () => console.log(`[${new Date().toISOString()}] 🎉 Server started on port ${port}`));

// Set keep-alive timeout to 65 seconds (<= Nginx's keepalive_timeout of 70s, since nodejs should be
// the principle connection lifetime handler, with Nginx just a failover)
serverInstance.keepAliveTimeout = 65000;
serverInstance.headersTimeout = 66000; // slightly higher than keepAliveTimeout
serverInstance.requestTimeout = 30 * 60 * 1000; // 15 minutes