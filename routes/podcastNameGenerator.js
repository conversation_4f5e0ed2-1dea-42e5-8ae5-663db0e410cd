
const https = require('https');
const rateLimit = require('express-rate-limit');

module.exports = (app) => {
  const limiter = rateLimit({
    windowMs: 1000 * 60 * 5, // 5 minutes
    max: 100, // limit each IP to 100 requests per 5 minutes
    message: 'Too many requests from this IP, please try again later.',
  });

  app.post('/podcastNameGenerator', limiter, async (req, res) => {
    try {
      // Extract model from request body or use default
      const model = req.body.model || 'gemini-2.5-flash';

      // Validate that we have the required API key
      if (!process.env.GOOGLE_API_KEY) {
        return res.status(500).json({
          error: 'Google API key not configured',
        });
      }

      // Prevent abuse by limiting input.
      let totalCharacters = 0;
      let hasInlineData = false;
      let hasFileData = false;
      req.body.contents.forEach((c) => {
        c.parts?.forEach((p) => {
          totalCharacters += p.text?.length || 0;
          if (p.inlineData) hasInlineData = true;
          if (p.fileData) hasFileData = true;
        });
      });
      req.body.systemInstruction?.parts.forEach((p) => {
        totalCharacters += p.text?.length || 0;
      });

      if (totalCharacters > 10000) {
        return res.status(400).json({
          error: 'Input too long',
        });
      }
      if (hasInlineData) {
        return res.status(400).json({
          error: 'Inline data not supported',
        });
      }
      if (hasFileData) {
        return res.status(400).json({
          error: 'File data not supported',
        });
      }

      // Prepare the request data for Google Gemini API
      if (!req.body.generationConfig) req.body.generationConfig = {};
      if (!req.body.generationConfig.thinkingConfig) req.body.generationConfig.thinkingConfig = {};
      req.body.generationConfig.thinkingConfig.thinkingBudget = 0;
      const geminiRequestData = JSON.stringify(req.body);

      // Construct the Gemini API URL
      const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${process.env.GOOGLE_API_KEY}`;
      const url = new URL(geminiUrl);

      // Configure the HTTPS request options
      const options = {
        hostname: url.hostname,
        port: 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(geminiRequestData),
        },
      };

      // Make the request to Google Gemini API
      const geminiRequest = https.request(options, (geminiResponse) => {
        let responseData = '';

        // Collect response data
        geminiResponse.on('data', (chunk) => {
          responseData += chunk;
        });

        // Handle response completion
        geminiResponse.on('end', () => {
          try {
            // Forward the status code from Gemini API
            res.status(geminiResponse.statusCode);

            // Forward response headers (excluding some that might cause issues)
            const headersToForward = ['content-type', 'cache-control'];
            headersToForward.forEach(header => {
              if (geminiResponse.headers[header]) {
                res.set(header, geminiResponse.headers[header]);
              }
            });

            // Try to parse and forward JSON response, or send raw response
            try {
              const jsonResponse = JSON.parse(responseData);
              res.json(jsonResponse);
            } catch (parseError) {
              // If response is not JSON, send as text
              res.send(responseData);
            }
          } catch (error) {
            console.error('Error processing Gemini API response:', error);
            res.status(500).json({
              error: 'Error processing response from Gemini API',
            });
          }
        });
      });

      // Handle request errors
      geminiRequest.on('error', (error) => {
        console.error('Error calling Gemini API:', error);
        res.status(500).json({
          error: 'Failed to connect to Gemini API',
          details: error.message,
        });
      });

      // Send the request data
      geminiRequest.write(geminiRequestData);
      geminiRequest.end();

    } catch (error) {
      console.error('Error in podcastNameGenerator route:', error);
      res.status(500).json({
        error: 'Internal server error',
        details: error.message,
      });
    }
  });
}
